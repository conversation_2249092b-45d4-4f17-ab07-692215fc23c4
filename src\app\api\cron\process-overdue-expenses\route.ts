import { NextRequest, NextResponse } from 'next/server';
import { processOverdueExpenses } from '@/services/billing/overdue-service';
import { CronLockManager } from '@/services/classes/cron-lock-manager';

// Configuração para runtime do Next.js
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 300; // 5 minutos de timeout

/**
 * Chave secreta para proteger o endpoint do cron job
 * Deve ser definida no .env como CRON_SECRET_KEY
 */
const CRON_SECRET_KEY = process.env.CRON_SECRET_KEY;

/**
 * Contador de execuções para estatísticas
 */
let executionCount = 0;

/**
 * Valida se a requisição está autorizada
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Verificar se a chave secreta está configurada
  if (!CRON_SECRET_KEY) {
    console.error('🚨 CRON_SECRET_KEY não configurada');
    return { isValid: false, error: 'Cron job não configurado' };
  }

  // Verificar header de autorização
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!providedKey || providedKey !== CRON_SECRET_KEY) {
    console.error('🚨 Tentativa de acesso não autorizada ao cron job de despesas em atraso');
    return { isValid: false, error: 'Não autorizado' };
  }

  return { isValid: true };
}

/**
 * Endpoint principal do cron job para processar despesas em atraso
 * Método: POST
 * Header: Authorization: Bearer <CRON_SECRET_KEY>
 * 
 * Execução programada: Diário às 06:00
 * Função: 
 * - Processar despesas em atraso
 * - Atualizar status para overdue
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  console.log(`🔄 [${requestId}] Iniciando cron job de processamento de despesas em atraso...`);

  try {
    // Validar requisição
    const validation = validateCronRequest(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: validation.error,
          timestamp: new Date().toISOString(),
          requestId
        },
        { status: validation.error === 'Não autorizado' ? 401 : 409 }
      );
    }

    // Criar gerenciador de lock
    const lockManager = new CronLockManager('process-overdue-expenses', 5); // 5 minutos de timeout

    // Executar com lock automático
    const lockResult = await lockManager.executeWithLock(requestId, async () => {
      executionCount++;
      console.log(`🚀 [${requestId}] Execução #${executionCount} autorizada`);

      // Executar processamento de despesas em atraso
      return await processOverdueExpenses();
    });

    const executionTime = Date.now() - startTime;

    // Verificar se conseguiu adquirir o lock
    if (!lockResult.lockAcquired) {
      console.warn(`⚠️ [${requestId}] Lock não adquirido: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Não foi possível adquirir lock',
        lockAcquired: false,
        timestamp: new Date().toISOString(),
        requestId
      }, { status: 409 });
    }

    // Verificar se a execução foi bem-sucedida
    if (!lockResult.success || !lockResult.data) {
      console.error(`❌ [${requestId}] Erro durante execução: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Erro durante execução',
        lockAcquired: true,
        executionTimeMs: executionTime,
        requestId,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    const result = lockResult.data;

    // Log do resultado
    console.log(`✅ [${requestId}] Cron job de despesas em atraso concluído em ${executionTime}ms:`);
    console.log(`  - Total processadas: ${result.data?.totalProcessed}`);
    console.log(`  - Atualizadas: ${result.data?.updated}`);
    console.log(`  - Erros: ${result.data?.errors.length || 0}`);

    // Verificar se houve erros
    if (result.data?.errors && result.data.errors.length > 0) {
      console.error(`❌ [${requestId}] Erros durante execução:`, result.data.errors);
    }

    return NextResponse.json({
      success: result.success,
      data: {
        ...result,
        executionId: requestId,
        cronExecutionCount: executionCount,
        apiExecutionTimeMs: executionTime,
        lockAcquired: true
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    console.error(`💥 [${requestId}] Erro crítico no cron job de despesas em atraso:`, error);

    return NextResponse.json({
      success: false,
      error: 'Erro interno do cron job',
      details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
      executionTimeMs: executionTime,
      requestId,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Endpoint para verificar status do cron job
 * Método: GET
 */
export async function GET(request: NextRequest) {
  // Verificar autorização (mesma chave do POST)
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!CRON_SECRET_KEY || !providedKey || providedKey !== CRON_SECRET_KEY) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    );
  }

  try {
    // Verificar quantas despesas precisam ser processadas
    const { createClient } = await import('@/services/supabase/server');
    const supabase = await createClient();
    const today = new Date().toISOString().split('T')[0];

    const { data: pendingExpenses, error } = await supabase
      .from('expenses')
      .select('id, due_date, amount, supplier_name')
      .eq('status', 'pending')
      .lt('due_date', today);

    if (error) {
      throw error;
    }

    // Verificar locks ativos
    const lockManager = new CronLockManager('process-overdue-expenses');
    const lockStatus = await lockManager.hasActiveLock();

    return NextResponse.json({
      status: 'healthy',
      isRunning: lockStatus.hasLock,
      runningBy: lockStatus.lockedBy || null,
      lockExpiresAt: lockStatus.expiresAt || null,
      executionCount,
      expensesNeedingProcessing: {
        count: pendingExpenses?.length || 0,
        totalAmount: pendingExpenses?.reduce((sum, e) => sum + parseFloat(e.amount), 0) || 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao verificar status do cron job de despesas em atraso:', error);
    return NextResponse.json({
      status: 'error',
      error: 'Erro ao verificar status',
      executionCount,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Bloquear outros métodos HTTP
 */
export async function PUT() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}
